<div class="text-center mt-10">
  <h2 class="text-5xl font-righteous uppercase text-pink-600 mb-10 relative">
    Projects
    <span class="block w-24 h-1 bg-pink-400 mt-2 mx-auto rounded"></span>
  </h2>
</div>

<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-10 px-6">
  <div *ngFor="let project of projects"
    class="bg-white border border-gray-200 rounded-2xl p-6 shadow-md hover:shadow-xl transition duration-300 flex flex-col justify-between">

    <div class="overflow-hidden rounded-xl mb-4 h-52">
      <img [src]="project.photo" alt="Project Image"
        class="w-full h-full object-cover hover:scale-105 transition duration-300" />
    </div>

    <div class="text-gray-800">
      <h3 class="text-2xl font-bold font-righteous uppercase text-gray-900">{{ project.name }}</h3>
      <p class="text-sm font-lato uppercase tracking-wide text-pink-600 mt-1">{{ project.title }}</p>
      <p class="text-sm mt-2 text-gray-700">{{ project.description }}</p>

      <div class="mt-4">
        <strong class="text-pink-600">Skills:</strong>
        <div class="flex flex-wrap gap-2 mt-1">
          <span *ngFor="let skill of project.skills"
            class="bg-pink-100 text-pink-700 text-xs font-semibold px-2 py-1 rounded-full">
            {{ skill }}
          </span>
        </div>
      </div>
    </div>

    <div class="mt-6 flex justify-between">
      <a [href]="project.githubLink" target="_blank"
        class="text-xs uppercase px-4 py-2 border border-pink-500 text-pink-500 rounded-full hover:bg-pink-500 hover:text-white transition">
        GitHub
      </a>
      <a [href]="project.link" target="_blank"
        class="text-xs uppercase px-4 py-2 border border-pink-500 text-pink-500 rounded-full hover:bg-pink-500 hover:text-white transition">
        Live Demo
      </a>
    </div>
  </div>
</div>
