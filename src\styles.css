@tailwind base;
@tailwind components;
@tailwind utilities;

/* Global Color Scheme - Pink/Rose Theme from Skills Component */
:root {
  /* Primary Colors */
  --primary-pink: #db2777; /* pink-600 */
  --primary-pink-dark: #be185d; /* pink-700 */
  --primary-pink-light: #f9a8d4; /* pink-300 */
  --primary-pink-lighter: #fce7f3; /* pink-100 */
  --primary-pink-lightest: #fdf2f8; /* pink-50 */

  /* Rose Colors */
  --rose-light: #fef7f7; /* rose-50 */
  --rose-lighter: #ffe4e6; /* rose-100 */

  /* Accent Colors */
  --accent-pink: #f472b6; /* pink-400 */
  --border-pink: #fbcfe8; /* pink-200 */
  --border-pink-dark: #f9a8d4; /* pink-300 */

  /* Background Gradients */
  --bg-gradient-start: var(--primary-pink-lightest);
  --bg-gradient-middle: #ffffff;
  --bg-gradient-end: var(--rose-lighter);

  /* Shadow Colors */
  --shadow-pink: rgba(251, 207, 232, 0.5); /* pink-200 with opacity */

  /* Text Colors */
  --text-primary: var(--primary-pink);
  --text-secondary: var(--primary-pink-dark);
}

/* Global Font and Base Styles */
*{
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

/* Apply global pink theme colors and prevent horizontal scroll */
html, body {
  overflow-x: hidden; /* Prevent horizontal scrolling */
  max-width: 100vw;   /* Ensure content doesn't exceed viewport width */
}

body {
  color: var(--text-secondary);
}

/* Global utility classes for consistent theming */
.theme-bg-gradient {
  background: linear-gradient(to bottom right, var(--bg-gradient-start), var(--bg-gradient-middle), var(--bg-gradient-end));
}

.theme-text-primary {
  color: var(--text-primary);
}

.theme-text-secondary {
  color: var(--text-secondary);
}

.theme-border {
  border-color: var(--border-pink);
}

.theme-bg-light {
  background-color: var(--primary-pink-lighter);
}

.theme-shadow {
  box-shadow: 0 4px 6px -1px var(--shadow-pink);
}
