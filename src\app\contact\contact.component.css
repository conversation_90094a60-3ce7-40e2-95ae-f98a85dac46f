/* التنسيق العام */
* {
  margin-bottom: 1rem;
}

.contact-header {
  text-align: center;
  color: var(--text-primary);
  padding: 1rem;
  position: relative;
  font-family: "Pacifico", cursive;
  font-size: 2rem;
}

.contact-header:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 150px;
  height: 4px;
  background-color: var(--accent-pink);
  border-radius: 2px;
}

/* تنسيق الحاوية */
.container {
  display: flex;
  align-items: center;
  gap: clamp(2rem, 8vw, 9rem); /* Responsive gap that won't cause overflow */
  max-width: 100vw;             /* Ensure container doesn't exceed viewport */
  padding: 0 1rem;              /* Add padding to prevent edge overflow */
}

/* SVG and Form adjustments */
svg {
  width: 30%;
  height: auto;
  animation: float 2s ease-in-out infinite;
  margin-left: 10%;
}

form {
  width: 35%; /* عرض النموذج */
  background-color: #fff;
  padding: 2rem;
  border-radius: 3rem;
  box-shadow: 0px 10px 20px rgba(0, 0, 0, 0.1);
  margin-right: 10%;
}

.title {
  font-family: 'Pacifico', cursive;
  color: var(--text-primary);
  font-size: 2.5rem;
  text-align: center;
  margin-bottom: 1.5rem;
}

.form-group {
  position: relative;
  margin-bottom: 1.5rem;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 1rem;
  padding-left: 3rem;
  font-size: 1.1rem;
  color: var(--text-secondary);
  background-color: var(--primary-pink-lightest);
  border: none;
  border-radius: 2rem;
  box-shadow: 0px 7px 5px var(--shadow-pink);
}

.form-group textarea {
  resize: none;
  height: 7rem;
}

.form-group .icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--primary-pink);
}

::placeholder {
  color: var(--primary-pink-light);
  font-weight: 600;
}

.btn.btn-primary {
  display: inline-block;
  width: 100%;
  padding: 0.8rem;
  font-size: 1.1rem;
  font-weight: bold;
  border: none;
  border-radius: 3rem;
  background: linear-gradient(131deg, var(--primary-pink), var(--accent-pink), var(--primary-pink-light), var(--primary-pink-dark));
  background-size: 300% 100%;
  transition: all 0.3s ease-in-out;
  color: #fff;
  cursor: pointer;
}

.btn.btn-primary:hover {
  box-shadow: 0px 7px 5px var(--shadow-pink);
  background-size: 100% 100%;
  transform: translateY(-0.15em);
}

/* تأثيرات الحركة */
@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
  100% { transform: translateY(0px); }
}

@keyframes blink {
  0% { opacity: 0; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .container {
    flex-direction: column;
    align-items: center;
    gap: 2rem; /* Adjusts gap between elements on smaller screens */
  }

  svg {
    width: 60%; /* Increases SVG width for smaller screens */
    margin-left: 0;
  }

  form {
    width: 90%; /* Makes form full width on smaller screens */
    margin-right: 0;
  }

  .title {
    font-size: 2rem; /* Adjusts title font size */
  }

  .btn.btn-primary {
    font-size: 1rem; /* Adjusts button font size */
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 1.8rem;
  }

  .contact-header {
    font-size: 1.5rem;
  }

  form {
    padding: 1.5rem;
  }

  .btn.btn-primary {
    padding: 0.6rem;
  }
}
